"""
FIJI/ImageJ Simple Batch Image Counter Script

This script:
1. Allows interactive folder selection
2. Processes all images of a specified type (e.g., jpg, tif, png)
3. Runs a user-specified macro on each image
4. Counts the number of entries in the results table
5. Saves results to a CSV file in the selected directory

Configuration:
- IMAGE_EXTENSIONS: List of file extensions to process
- MACRO_PATH: Path to the ImageJ macro file to run
- DEFAULT_START_DIR: where the folder chooser shall start (if none or
not existent, starts at highest level)
"""

# === IMPORTS ===
from ij import IJ
from ij.measure import ResultsTable
from java.io import File, FileWriter, BufferedWriter
from javax.swing import JFileChooser
import os
import time

# === CONFIGURATION ===
IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.tif', '.tiff', '.png', '.bmp']  # Extensions to process - remove those that shall not be processed
MACRO_PATH = "C:\Users\<USER>\Fiji.app\scripts\wiretip-counting_1.1_for_batch.ijm"  # Path to macro file - will be set via dialog if empty
DEFAULT_START_DIR = "C:/Studium/10Semester/Maria"				# Directory where the chooser starts (optimally close your destination folder)

# === UTILITY FUNCTIONS ===
def safe_log(message):
    """Safe logging function that handles special Unicode characters (like µ)."""
    try:
        IJ.log(message)
    except:
        # Fallback for Unicode issues
        try:
            ascii_message = message.encode('ascii', 'replace').decode('ascii')
            IJ.log(ascii_message)
        except:
            IJ.log("Log message contained unsupported characters")

def is_headless_mode():
    """Check if running in headless mode."""
    try:
        return IJ.getInstance() is None
    except:
        return True

def show_folder_selection_dialog():
    """Show folder selection dialog."""
    if is_headless_mode():
        return None
    
    try:
        chooser = JFileChooser()
        chooser.setDialogTitle("Select Directory Containing Images to Process")
        chooser.setFileSelectionMode(JFileChooser.DIRECTORIES_ONLY)
        
        # Start in specified default directory
        if os.path.exists(DEFAULT_START_DIR):
            chooser.setCurrentDirectory(File(DEFAULT_START_DIR))
        else:
            chooser.setCurrentDirectory(File(os.getcwd()))
        
        if chooser.showOpenDialog(None) == JFileChooser.APPROVE_OPTION:
            return chooser.getSelectedFile().getAbsolutePath()
    except:
        pass
    return None

def show_file_selection_dialog(title, file_filter=None):
    """Show file selection dialog."""
    if is_headless_mode():
        return None
    
    try:
        chooser = JFileChooser()
        chooser.setDialogTitle(title)
        chooser.setFileSelectionMode(JFileChooser.FILES_ONLY)
        
        if os.path.exists(DEFAULT_START_DIR):
            chooser.setCurrentDirectory(File(DEFAULT_START_DIR))
        else:
            chooser.setCurrentDirectory(File(os.getcwd()))
        
        if chooser.showOpenDialog(None) == JFileChooser.APPROVE_OPTION:
            return chooser.getSelectedFile().getAbsolutePath()
    except:
        pass
    return None


def get_image_files(directory, extensions):
    """Get list of image files in directory with specified extensions."""
    image_files = []
    try:
        for filename in os.listdir(directory):
            file_lower = filename.lower()
            if any(file_lower.endswith(ext.lower()) for ext in extensions):
                full_path = os.path.join(directory, filename)
                if os.path.isfile(full_path):
                    image_files.append(full_path)
        image_files.sort()
    except Exception as e:
        safe_log("ERROR: Failed to list files in directory: " + str(e))
    
    return image_files

def process_single_image(image_path, macro_path):
    """Process a single image and return the results from results table."""
    try:
        # Open image
        imp = IJ.openImage(image_path)
        if not imp:
            safe_log("ERROR: Could not open image: " + os.path.basename(image_path))
            return None
        
        imp.show()

        # Clear any existing results
        rt = ResultsTable.getResultsTable()
        if rt:
            rt.reset()

        # Run the macro
        image_title = imp.getTitle()
        safe_log("  -> Running macro on: " + image_title)
        IJ.runMacroFile(macro_path, image_title)

        # Get results from table
        rt = ResultsTable.getResultsTable()
        if rt is None or rt.getCounter() == 0:
            safe_log("  -> No results generated")
            # Clean up
            imp.changes = False
            imp.close()
            return (0, 0.0, 0.0)  # tip_count, area, tips_per_um2

        # Extract values from first row using column names
        tip_count = int(rt.getValue("Tip Count", 0)) if rt.getCounter() > 0 else 0
        area_um2 = float(rt.getValue("Area [um^2]", 0)) if rt.getCounter() > 0 else 0.0
        tips_per_um2 = float(rt.getValue("Tips per um^2", 0)) if rt.getCounter() > 0 else 0.0

        safe_log("  -> Tip count: {}, Area: {:.2f} um^2, Tips/um^2: {:.2f}".format(
            tip_count, area_um2, tips_per_um2))

        # Clean up
        imp.changes = False
        imp.close()

        return (tip_count, area_um2, tips_per_um2)

    except Exception as e:
        safe_log("ERROR: Failed to process image {}: {}".format(os.path.basename(image_path), str(e)))
        return None

def write_csv_results(csv_path, results_data):
    """Write results to CSV file."""
    try:
        writer = BufferedWriter(FileWriter(File(csv_path)))

        # Write header
        writer.write("Image,Tip_Count,Area_um2,Tips_per_um2\n")

        # Write data and calculate totals
        total_tip_count = 0
        total_area = 0.0
        for image_name, (tip_count, area_um2, tips_per_um2) in results_data:
            writer.write("{},{},{:.2f},{:.2f}\n".format(
                image_name, tip_count, area_um2, tips_per_um2))
            total_tip_count += tip_count
            total_area += area_um2

        # Calculate overall tips per um2
        overall_tips_per_um2 = total_tip_count / total_area if total_area > 0 else 0.0

        # Write total
        writer.write("TOTAL,{},{:.2f},{:.2f}\n".format(
            total_tip_count, total_area, overall_tips_per_um2))

        writer.close()
        safe_log("CSV file saved successfully: " + csv_path)
        safe_log("Total tips: {}, Total area: {:.2f} um^2, Overall tips/um^2: {:.2f}".format(
            total_tip_count, total_area, overall_tips_per_um2))

    except Exception as e:
        safe_log("ERROR: Failed to write CSV file: " + str(e))

# === MAIN PROCESSING ===
def main():
    """Main processing function."""
    start_time = time.time()
    
    IJ.log("\\Clear")
    safe_log("=" * 60)
    safe_log("FIJI/ImageJ Simple Batch Image Counter")
    safe_log("=" * 60)
    
    # Get input directory
    input_dir = show_folder_selection_dialog()
    if not input_dir:
        safe_log("ERROR: No directory selected")
        return False
    
    safe_log("Selected directory: " + input_dir)
    
    # Get macro file if not configured
    global MACRO_PATH
    if not MACRO_PATH or not os.path.exists(MACRO_PATH):
        safe_log("Please select the macro file to run...")
        MACRO_PATH = show_file_selection_dialog("Select ImageJ Macro File")
        if not MACRO_PATH:
            safe_log("ERROR: No macro file selected")
            return False
    
    safe_log("Using macro: " + MACRO_PATH)
    
    # Get image files
    image_files = get_image_files(input_dir, IMAGE_EXTENSIONS)
    if not image_files:
        safe_log("ERROR: No image files found with extensions: " + str(IMAGE_EXTENSIONS))
        return False
    
    safe_log("Found {} image files to process".format(len(image_files)))
    
    # Process images
    results_data = []
    processed_count = 0
    
    safe_log("\nProcessing images...")
    safe_log("-" * 40)
    
    for i, image_path in enumerate(image_files):
        image_name = os.path.basename(image_path)
        safe_log("Processing {}/{}: {}".format(i + 1, len(image_files), image_name))
        
        result = process_single_image(image_path, MACRO_PATH)
        if result is not None:
            results_data.append((image_name, result))
            processed_count += 1
        
        # Progress indicator
        if len(image_files) > 1:
            percent = ((i + 1) * 100) // len(image_files)
            safe_log("  -> Progress: {}%".format(percent))
        
        safe_log("")  # Visual separation
       

    # Close results table window
    try:
        rt = ResultsTable.getResultsTable()
        if rt:
            rt.show("Results")
            IJ.selectWindow("Results")
            IJ.run("Close")
    except Exception as e:
        safe_log("Note: Could not close results table: " + str(e))
    
    # Write results to CSV
    if results_data:
        csv_filename = "wiretip_analysis_results.csv"
        csv_path = os.path.join(input_dir, csv_filename)
        write_csv_results(csv_path, results_data)
        
        # Summary
        safe_log("-" * 40)
        safe_log("PROCESSING COMPLETE")
        safe_log("Images processed: {}/{}".format(processed_count, len(image_files)))
        safe_log("Results saved to: " + csv_path)
        
        elapsed_time = time.time() - start_time
        safe_log("Total processing time: {:.1f} seconds".format(elapsed_time))
        
        return True
    else:
        safe_log("ERROR: No images were successfully processed")
        return False

# === SCRIPT EXECUTION ===
if __name__ == "__main__":
    try:
        success = main()
        if success:
            safe_log("\nSUCCESS: Script completed successfully")
        else:
            safe_log("\nFAILED: Script completed with errors")
    except Exception as e:
        safe_log("FATAL ERROR: " + str(e))
        import traceback
        traceback.print_exc()
